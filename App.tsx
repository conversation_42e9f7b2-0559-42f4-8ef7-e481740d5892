/**
 * PDF Viewer App
 *
 * @format
 */

import React, {useEffect} from 'react';
import {StatusBar, useColorScheme, Alert} from 'react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import 'react-native-gesture-handler';
// RAG backend initialization is now handled in ChatContext

// Import navigation
import AppNavigator from './src/navigation/AppNavigator';

// Import contexts
import {PDFProvider} from './src/context/PDFContext';
import {SettingsProvider} from './src/context/SettingsContext';
import {ChatProvider} from './src/context/ChatContext';

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  // App initialization - RAG backend is now initialized in ChatContext
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('Initializing app...');
        // RAG backend initialization is handled by ChatContext when needed
        console.log('App initialization complete');
      } catch (error) {
        console.error('Failed to initialize app:', error);
        Alert.alert(
          'Initialization Error',
          'Failed to initialize the app. Please try restarting the app.',
        );
      }
    };

    initializeApp();
  }, []);

  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      <SettingsProvider>
        <PDFProvider>
          <ChatProvider>
            <AppNavigator />
          </ChatProvider>
        </PDFProvider>
      </SettingsProvider>
    </SafeAreaProvider>
  );
}

export default App;

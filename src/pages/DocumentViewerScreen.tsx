import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  Share,
  Dimensions,
  Alert,
} from 'react-native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';
import {RootStackParamList} from '../navigation/types';
import Pdf from 'react-native-pdf';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import {colors, spacing, typography} from '../theme/theme';
import {usePDFContext} from '../context/PDFContext';
import {useSettings} from '../context/SettingsContext';
import {DocumentMetadata} from '../utils/documentStorage';
import {getFilenameFromPath} from '../utils/fileUtils';

type DocumentViewerScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'DocumentViewer'
>;
type DocumentViewerScreenRouteProp = RouteProp<
  RootStackParamList,
  'DocumentViewer'
>;

interface DocumentViewerScreenProps {
  navigation: DocumentViewerScreenNavigationProp;
  route: DocumentViewerScreenRouteProp;
}

const DocumentViewerScreen: React.FC<DocumentViewerScreenProps> = ({
  navigation,
  route,
}) => {
  const {documentId} = route.params;
  const {documents, openDocument, isLoading} = usePDFContext();
  const {settings} = useSettings();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [scale, setScale] = useState(settings.pdfViewer.defaultZoom);

  // PDF viewer reference
  const pdfRef = useRef<Pdf>(null);

  // Get current document
  const document = documents.find(d => d.id === documentId);

  // Update last opened timestamp on mount
  useEffect(() => {
    if (!document) {
      Alert.alert('Error', 'Document not found');
      navigation.goBack();
      return;
    }

    const updateOpened = async () => {
      try {
        await openDocument(documentId);
      } catch (error) {
        console.error('Error updating last opened:', error);
      }
    };

    updateOpened();
  }, [documentId, navigation, openDocument]);

  // Handle zoom in
  const handleZoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.25, 3.0));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.25, 0.5));
  };

  // Handle page change
  const handlePageChange = (page: number, totalPages: number) => {
    setCurrentPage(page);
    setTotalPages(totalPages);
  };

  // Handle next page
  const handleNextPage = () => {
    if (currentPage < totalPages && pdfRef.current) {
      pdfRef.current.setPage(currentPage + 1);
    }
  };

  // Handle previous page
  const handlePrevPage = () => {
    if (currentPage > 1 && pdfRef.current) {
      pdfRef.current.setPage(currentPage - 1);
    }
  };

  // Handle share
  const handleShare = async () => {
    if (!document) return;

    try {
      await Share.share({
        title: document.title,
        url: `file://${document.path}`,
        message: `Check out this PDF: ${document.title}`,
      });
    } catch (error) {
      console.error('Error sharing document:', error);
      Alert.alert('Error', 'Failed to share document');
    }
  };

  // Get document title
  const getDocumentTitle = () => {
    if (!document) return 'Document not found';
    return document.title || getFilenameFromPath(document.path);
  };
  console.log('document', document);
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text variant="h2" numberOfLines={1} style={styles.title}>
            {getDocumentTitle()}
          </Text>
          <Text variant="caption">
            Page {currentPage} of {totalPages}
          </Text>
        </View>
        <Button
          title="Close"
          variant="outline"
          size="small"
          onPress={() => navigation.goBack()}
        />
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text variant="body" style={styles.loadingText}>
            Loading document...
          </Text>
        </View>
      ) : document ? (
        <View style={styles.viewerContainer}>
          <Pdf
            ref={pdfRef}
            source={{uri: document.path}}
            style={styles.pdf}
            scale={scale}
            spacing={0}
            horizontal={false}
            enablePaging={settings.pdfViewer.singlePageMode}
            // fitWidth is not available in the current version
            // Using scale to simulate fit to width
            enableAnnotationRendering={settings.pdfViewer.enableAnnotations}
            onLoadComplete={numberOfPages => {
              setTotalPages(numberOfPages);
            }}
            onPageChanged={handlePageChange}
            onError={error => {
              console.error('PDF error:', error);
              Alert.alert('Error', 'Failed to load PDF');
            }}
            onPressLink={uri => {
              console.log(`Link pressed: ${uri}`);
            }}
          />
        </View>
      ) : (
        <View style={styles.errorContainer}>
          <Text variant="body">Document not found</Text>
        </View>
      )}

      <View style={styles.toolbar}>
        <View style={styles.toolbarGroup}>
          <Button
            title="Prev"
            size="small"
            onPress={handlePrevPage}
            disabled={currentPage <= 1}
          />
          <Text variant="body" style={styles.pageIndicator}>
            {currentPage}/{totalPages}
          </Text>
          <Button
            title="Next"
            size="small"
            onPress={handleNextPage}
            disabled={currentPage >= totalPages}
          />
        </View>

        <View style={styles.toolbarGroup}>
          <Button
            title="-"
            size="small"
            onPress={handleZoomOut}
            disabled={scale <= 0.5}
          />
          <Text variant="body" style={styles.zoomIndicator}>
            {Math.round(scale * 100)}%
          </Text>
          <Button
            title="+"
            size="small"
            onPress={handleZoomIn}
            disabled={scale >= 3.0}
          />
        </View>

        <View style={styles.toolbarGroup}>
          <Button
            title="Chat"
            variant="secondary"
            size="small"
            onPress={() => navigation.navigate('Chat', {documentId})}
          />
          <Button
            title="Share"
            variant="outline"
            size="small"
            onPress={handleShare}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  titleContainer: {
    flex: 1,
    marginRight: spacing.md,
  },
  title: {
    fontSize: typography.fontSizes.xl,
  },
  viewerContainer: {
    flex: 1,
    backgroundColor: '#F2F2F2',
  },
  pdf: {
    flex: 1,
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
    backgroundColor: '#F2F2F2',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.background,
  },
  toolbarGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  pageIndicator: {
    marginHorizontal: spacing.sm,
    minWidth: 50,
    textAlign: 'center',
  },
  zoomIndicator: {
    marginHorizontal: spacing.sm,
    minWidth: 50,
    textAlign: 'center',
  },
});

export default DocumentViewerScreen;

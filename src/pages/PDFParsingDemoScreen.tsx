import React, {useState} from 'react';
import {View, StyleSheet, ScrollView, Alert} from 'react-native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';
import {RootStackParamList} from '../navigation/types';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import {colors, spacing} from '../theme/theme';
import {usePDFContext} from '../context/PDFContext';
import {PDFWebViewParser} from '../components/PDFWebViewParser';
import {ExtractedTextContent} from '../utils/textExtraction';

type PDFParsingDemoScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Home'
>;

type PDFParsingDemoScreenRouteProp = RouteProp<RootStackParamList, 'Home'>;

interface PDFParsingDemoScreenProps {
  navigation: PDFParsingDemoScreenNavigationProp;
  route: PDFParsingDemoScreenRouteProp;
}

const PDFParsingDemoScreen: React.FC<PDFParsingDemoScreenProps> = ({
  navigation: _navigation,
}) => {
  const {documents} = usePDFContext();
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);
  const [parsedContent, setParsedContent] =
    useState<ExtractedTextContent | null>(null);
  const [isParserReady, setIsParserReady] = useState(false);
  const [isParsing, setIsParsing] = useState(false);
  const [showWebView, setShowWebView] = useState(false);

  const handleDocumentSelect = (documentId: string) => {
    setSelectedDocument(documentId);
    setParsedContent(null);
  };

  const handleParseComplete = (result: ExtractedTextContent) => {
    setParsedContent(result);
    setIsParsing(false);
    setShowWebView(false);

    if (result.isProcessed) {
      Alert.alert(
        'Parsing Complete',
        `Successfully extracted text from ${result.pageTexts.length} pages with ${result.chunks.length} text chunks.`,
      );
    } else {
      Alert.alert('Parsing Failed', result.processingError || 'Unknown error');
    }
  };

  const handleParseError = (error: string) => {
    setIsParsing(false);
    setShowWebView(false);
    Alert.alert('Parsing Error', error);
  };

  const handleParserReady = () => {
    setIsParserReady(true);
  };

  const startParsing = () => {
    if (!selectedDocument) {
      Alert.alert('No Document', 'Please select a document first');
      return;
    }

    if (!isParserReady) {
      Alert.alert('Parser Not Ready', 'WebView parser is still loading');
      return;
    }

    setIsParsing(true);
    setShowWebView(true);
    setParsedContent(null);
  };

  const selectedDoc = documents.find(doc => doc.id === selectedDocument);

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}>
        <Text variant="h1" style={styles.title}>
          PDF Parsing Demo
        </Text>

        <Text variant="body" style={styles.description}>
          This demo shows how to use the WebView-based PDF parser with PDF.js to
          extract text and positioning information from PDF documents.
        </Text>

        {/* Parser Status */}
        <View style={styles.section}>
          <Text variant="h3" style={styles.sectionTitle}>
            Parser Status
          </Text>
          <View
            style={[
              styles.statusIndicator,
              isParserReady ? styles.ready : styles.loading,
            ]}>
            <Text variant="body" style={styles.statusText}>
              {isParserReady
                ? '✓ WebView Parser Ready'
                : '⏳ Loading WebView Parser...'}
            </Text>
          </View>
        </View>

        {/* Document Selection */}
        <View style={styles.section}>
          <Text variant="h3" style={styles.sectionTitle}>
            Select Document
          </Text>
          {documents.length === 0 ? (
            <Text variant="body" style={styles.noDocuments}>
              No documents available. Import a PDF document first.
            </Text>
          ) : (
            documents.map(doc => (
              <Button
                key={doc.id}
                title={doc.title}
                onPress={() => handleDocumentSelect(doc.id)}
                style={[
                  styles.documentButton,
                  selectedDocument === doc.id && styles.selectedDocument,
                ]}
                variant={selectedDocument === doc.id ? 'primary' : 'secondary'}
              />
            ))
          )}
        </View>

        {/* Parse Button */}
        {selectedDoc && (
          <View style={styles.section}>
            <Text variant="h3" style={styles.sectionTitle}>
              Selected Document
            </Text>
            <Text variant="body" style={styles.selectedDocInfo}>
              {selectedDoc.title}
            </Text>
            <Text variant="caption" style={styles.selectedDocPath}>
              {selectedDoc.path}
            </Text>

            <Button
              title={isParsing ? 'Parsing...' : 'Parse with WebView'}
              onPress={startParsing}
              disabled={!isParserReady || isParsing}
              style={styles.parseButton}
            />
          </View>
        )}

        {/* Parsing Results */}
        {parsedContent && (
          <View style={styles.section}>
            <Text variant="h3" style={styles.sectionTitle}>
              Parsing Results
            </Text>

            <View style={styles.resultStats}>
              <Text variant="body" style={styles.stat}>
                Pages: {parsedContent.pageTexts.length}
              </Text>
              <Text variant="body" style={styles.stat}>
                Text Chunks: {parsedContent.chunks.length}
              </Text>
              <Text variant="body" style={styles.stat}>
                Status: {parsedContent.isProcessed ? 'Success' : 'Failed'}
              </Text>
            </View>

            {parsedContent.processingError && (
              <Text variant="body" style={styles.error}>
                Error: {parsedContent.processingError}
              </Text>
            )}

            {parsedContent.fullText && (
              <View style={styles.textPreview}>
                <Text variant="h4" style={styles.previewTitle}>
                  Text Preview
                </Text>
                <Text variant="caption" style={styles.previewText}>
                  {parsedContent.fullText.substring(0, 500)}
                  {parsedContent.fullText.length > 500 && '...'}
                </Text>
              </View>
            )}

            {parsedContent.chunks.length > 0 && (
              <View style={styles.chunksPreview}>
                <Text variant="h4" style={styles.previewTitle}>
                  First Few Text Chunks
                </Text>
                {parsedContent.chunks.slice(0, 3).map((chunk, _index) => (
                  <View key={chunk.id} style={styles.chunk}>
                    <Text variant="caption" style={styles.chunkId}>
                      {chunk.id}
                    </Text>
                    <Text variant="body" style={styles.chunkText}>
                      {chunk.text}
                    </Text>
                    <Text variant="caption" style={styles.chunkPosition}>
                      Page {chunk.pageNumber}, Position: (
                      {chunk.position.x.toFixed(1)},{' '}
                      {chunk.position.y.toFixed(1)})
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
      </ScrollView>

      {/* WebView Parser */}
      {selectedDoc && (
        <PDFWebViewParser
          filePath={selectedDoc.path}
          onParseComplete={handleParseComplete}
          onError={handleParseError}
          onReady={handleParserReady}
          visible={showWebView}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: spacing.lg,
  },
  title: {
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  description: {
    marginBottom: spacing.lg,
    textAlign: 'center',
    color: colors.textSecondary,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    marginBottom: spacing.md,
  },
  statusIndicator: {
    padding: spacing.md,
    borderRadius: 8,
    alignItems: 'center',
  },
  ready: {
    backgroundColor: '#e8f5e8',
    borderColor: '#4caf50',
    borderWidth: 1,
  },
  loading: {
    backgroundColor: '#fff3e0',
    borderColor: '#ff9800',
    borderWidth: 1,
  },
  statusText: {
    fontWeight: 'bold',
  },
  noDocuments: {
    textAlign: 'center',
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  documentButton: {
    marginBottom: spacing.sm,
  },
  selectedDocument: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  selectedDocInfo: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  selectedDocPath: {
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  parseButton: {
    marginTop: spacing.md,
  },
  resultStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.md,
    padding: spacing.md,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  stat: {
    textAlign: 'center',
    fontWeight: 'bold',
  },
  error: {
    color: colors.error,
    marginBottom: spacing.md,
  },
  textPreview: {
    marginBottom: spacing.md,
  },
  previewTitle: {
    marginBottom: spacing.sm,
  },
  previewText: {
    backgroundColor: '#f5f5f5',
    padding: spacing.md,
    borderRadius: 8,
    fontFamily: 'monospace',
  },
  chunksPreview: {
    marginBottom: spacing.md,
  },
  chunk: {
    backgroundColor: '#f5f5f5',
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.primary,
  },
  chunkId: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  chunkText: {
    marginBottom: spacing.xs,
  },
  chunkPosition: {
    color: colors.textSecondary,
  },
});

export default PDFParsingDemoScreen;

// New RAG-based text generation using react-native-rag and ExecuTorch
import {
  initRAGBackend,
  generateText as ragGenerateText,
  addDocumentToRAG,
  getActiveModelInfo as ragGetActiveModelInfo,
  isRAGReady as ragIsReady,
  createContextWindow as ragCreateContextWindow,
  addMessageToContext as ragAddMessageToContext,
  DEFAULT_GENERATION_PARAMS as RAG_DEFAULT_PARAMS,
  type GenerationParams as RAGGenerationParams,
  type ContextWindow as RAGContextWindow,
} from './ragBackend';

// Export types for compatibility
export type GenerationParams = RAGGenerationParams;
export type ContextWindow = RAGContextWindow;

// Export default params
export const DEFAULT_GENERATION_PARAMS = RAG_DEFAULT_PARAMS;

// Initialize the RAG backend (replaces initOnnxRuntime)
export const initTextGeneration = async (
  onProgress?: (progress: number) => void,
): Promise<void> => {
  try {
    await initRAGBackend(onProgress);
    console.log('Text generation initialized with RAG backend');
  } catch (error) {
    console.error('Failed to initialize text generation:', error);
    throw error;
  }
};

// Generate text using RAG backend
export const generateText = async (
  prompt: string,
  params: GenerationParams = DEFAULT_GENERATION_PARAMS,
  onToken?: (token: string) => void,
): Promise<string> => {
  try {
    if (!ragIsReady()) {
      console.log('RAG not ready, initializing...');
      await initRAGBackend();
    }

    return await ragGenerateText(prompt, params, onToken);
  } catch (error) {
    console.error('Error in generateText:', error);
    // Fallback to a simple response
    return `I apologize, but I'm having trouble processing your request right now. The system is still initializing. Please try again in a moment.`;
  }
};

// Get active model info
export const getActiveModelInfo = async () => {
  return await ragGetActiveModelInfo();
};

// Check if RAG is ready
export const isRAGReady = (): boolean => {
  return ragIsReady();
};

// Create context window
export const createContextWindow = (): ContextWindow => {
  return ragCreateContextWindow();
};

// Add message to context
export const addMessageToContext = (
  context: ContextWindow,
  role: 'user' | 'assistant' | 'system',
  content: string,
): ContextWindow => {
  return ragAddMessageToContext(context, role, content);
};

// Add document to RAG for retrieval
export const addDocumentForRetrieval = async (
  document: string,
  metadata?: Record<string, any>,
): Promise<string> => {
  try {
    if (!ragIsReady()) {
      console.log('RAG not ready, initializing...');
      await initRAGBackend();
    }

    return await addDocumentToRAG(document, metadata);
  } catch (error) {
    console.error('Error adding document to RAG:', error);
    throw error;
  }
};

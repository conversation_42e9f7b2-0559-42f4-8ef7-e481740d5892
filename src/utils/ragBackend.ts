import {RAG, MemoryVectorStore} from 'react-native-rag';

// Fallback implementations for when native modules aren't available
class FallbackLLM {
  async generate(prompt: string, options?: any): Promise<string> {
    console.log(
      'FallbackLLM: Generating response for prompt:',
      prompt.substring(0, 100) + '...',
    );
    // Simulate a response
    return `I'm a fallback response to: "${prompt.substring(
      0,
      50,
    )}...". The RAG backend is initializing and will be available soon.`;
  }
}

class FallbackEmbeddings {
  async embed(text: string): Promise<number[]> {
    console.log(
      'FallbackEmbeddings: Creating embedding for text:',
      text.substring(0, 50) + '...',
    );
    // Return a simple mock embedding (384 dimensions for ALL-MiniLM-L6-v2 compatibility)
    return new Array(384).fill(0).map(() => Math.random());
  }
}

// Try to import native modules with fallback
let ExecuTorchLLM: any = FallbackLLM;
let ExecuTorchEmbeddings: any = FallbackEmbeddings;
let LLAMA3_2_1B_QLORA: any = null;
let ALL_MINILM_L6_V2: any = null;

try {
  const executorchModule = require('@react-native-rag/executorch');
  ExecuTorchLLM = executorchModule.ExecuTorchLLM || FallbackLLM;
  ExecuTorchEmbeddings =
    executorchModule.ExecuTorchEmbeddings || FallbackEmbeddings;
  console.log('✅ ExecuTorch modules loaded successfully');
} catch (error: any) {
  console.log(
    '⚠️ ExecuTorch modules not available, using fallback:',
    error.message,
  );
}

try {
  const constantsModule = require('react-native-executorch');
  LLAMA3_2_1B_QLORA = constantsModule.LLAMA3_2_1B_QLORA;
  ALL_MINILM_L6_V2 = constantsModule.ALL_MINILM_L6_V2;
  console.log('✅ ExecuTorch constants loaded successfully');
} catch (error: any) {
  console.log(
    '⚠️ ExecuTorch constants not available, using fallback:',
    error.message,
  );
}

// RAG instance
let ragInstance: RAG | null = null;
let isInitialized = false;
let isInitializing = false;

// Generation parameters interface
export interface GenerationParams {
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stopSequences?: string[];
}

// Default generation parameters
export const DEFAULT_GENERATION_PARAMS: GenerationParams = {
  maxTokens: 512,
  temperature: 0.7,
  topP: 0.9,
  stopSequences: ['<|im_end|>', '</s>'],
};

// Context window interface for compatibility
export interface ContextWindow {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
}

// Create context window
export const createContextWindow = (): ContextWindow => {
  return {
    messages: [],
  };
};

// Add message to context
export const addMessageToContext = (
  context: ContextWindow,
  role: 'user' | 'assistant' | 'system',
  content: string,
): ContextWindow => {
  return {
    messages: [...context.messages, {role, content}],
  };
};

// Initialize RAG backend
export const initRAGBackend = async (
  onProgress?: (progress: number) => void,
): Promise<void> => {
  if (isInitialized || isInitializing) {
    return;
  }

  isInitializing = true;

  try {
    console.log('Initializing RAG backend...');

    // Create embeddings model for vector store
    let embeddings;
    if (ALL_MINILM_L6_V2 && ALL_MINILM_L6_V2.modelSource) {
      embeddings = new ExecuTorchEmbeddings({
        modelSource: ALL_MINILM_L6_V2.modelSource,
        tokenizerSource: ALL_MINILM_L6_V2.tokenizerSource,
        onDownloadProgress: (progress: number) => {
          console.log(
            `Embeddings model download progress: ${Math.round(
              progress * 100,
            )}%`,
          );
          onProgress?.(progress * 0.3); // First 30% for embeddings
        },
      });
    } else {
      console.log('Using fallback embeddings');
      embeddings = new FallbackEmbeddings();
    }

    // Create vector store with embeddings
    const vectorStore = new MemoryVectorStore({embeddings});

    // Create LLM
    let llm;
    if (LLAMA3_2_1B_QLORA && LLAMA3_2_1B_QLORA.modelSource) {
      llm = new ExecuTorchLLM({
        modelSource: LLAMA3_2_1B_QLORA.modelSource,
        tokenizerSource: LLAMA3_2_1B_QLORA.tokenizerSource,
        tokenizerConfigSource: LLAMA3_2_1B_QLORA.tokenizerConfigSource,
        onDownloadProgress: (progress: number) => {
          console.log(`LLM download progress: ${Math.round(progress * 100)}%`);
          onProgress?.(0.3 + progress * 0.7); // Remaining 70% for LLM
        },
        responseCallback: (response: any) => {
          // This will be handled by the callback in generateText
          console.log('LLM response chunk:', response);
        },
      });
    } else {
      console.log('Using fallback LLM');
      llm = new FallbackLLM();
    }

    // Create RAG instance
    ragInstance = new RAG({vectorStore, llm});

    // Load the RAG system (skip for fallback)
    if (LLAMA3_2_1B_QLORA && ALL_MINILM_L6_V2) {
      console.log('Loading RAG system...');
      await ragInstance.load();
    } else {
      console.log('Skipping RAG load for fallback mode');
    }

    isInitialized = true;
    console.log('RAG backend initialized successfully');
  } catch (error) {
    console.error('Error initializing RAG backend:', error);
    throw error;
  } finally {
    isInitializing = false;
  }
};

// Generate text using RAG
export const generateText = async (
  prompt: string,
  params: GenerationParams = DEFAULT_GENERATION_PARAMS,
  onToken?: (token: string) => void,
): Promise<string> => {
  if (!ragInstance || !isInitialized) {
    throw new Error(
      'RAG backend not initialized. Call initRAGBackend() first.',
    );
  }

  try {
    console.log('Generating text with RAG:', prompt);

    // Convert prompt to message format
    const messages = [{role: 'user' as const, content: prompt}];

    // Create a wrapper callback for debugging
    const debugCallback = onToken
      ? (token: string) => {
          console.log('LLM response chunk:', token);
          onToken(token);
        }
      : undefined;

    // Generate response with RAG
    const response = await ragInstance.generate(messages, {
      augmentedGeneration: true, // Use RAG by default
      k: 3, // Retrieve top 3 relevant documents
      callback: debugCallback,
    });

    console.log(
      'RAG generation completed, full response length:',
      response.length,
    );
    return response;
  } catch (error) {
    console.error('Error generating text:', error);
    throw error;
  }
};

// Add document to RAG vector store
export const addDocumentToRAG = async (
  document: string,
  metadata?: Record<string, any>,
): Promise<string> => {
  if (!ragInstance || !isInitialized) {
    throw new Error(
      'RAG backend not initialized. Call initRAGBackend() first.',
    );
  }

  try {
    console.log('Adding document to RAG vector store');

    // Split and add document to vector store
    const ids = await ragInstance.splitAddDocument(
      document,
      metadata ? () => [metadata] : undefined,
    );

    console.log(`Document added with ${ids.length} chunks`);
    return ids[0] || '';
  } catch (error) {
    console.error('Error adding document to RAG:', error);
    throw error;
  }
};

// Get model info for compatibility
export const getActiveModelInfo = async () => {
  return {
    id: 'llama3.2-1b-qlora',
    name: 'Llama 3.2 1B QLoRA',
    type: 'executorch',
    modelType: 'generative',
    isDownloaded: isInitialized,
    isFallback: false,
    path: 'executorch://llama3.2-1b-qlora',
  };
};

// Check if RAG is ready
export const isRAGReady = (): boolean => {
  return isInitialized && ragInstance !== null;
};

// Cleanup RAG backend
export const cleanupRAGBackend = async (): Promise<void> => {
  if (ragInstance) {
    try {
      await ragInstance.unload();
      ragInstance = null;
      isInitialized = false;
      console.log('RAG backend cleaned up');
    } catch (error) {
      console.error('Error cleaning up RAG backend:', error);
    }
  }
};

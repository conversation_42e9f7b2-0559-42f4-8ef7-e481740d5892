import {DocumentMetadata, updateDocument} from './documentStorage';
import {
  processDocumentText,
  ExtractedTextContent,
  checkPDFHasTextLayer,
} from './textExtraction';
import {processAndStoreDocumentVectors} from './vectorDatabase';

/**
 * Process a document's text content and update its metadata
 */
export const processDocument = async (
  document: DocumentMetadata,
): Promise<{
  success: boolean;
  document: DocumentMetadata;
  textContent?: ExtractedTextContent;
}> => {
  try {
    console.log(`Processing document: ${document.title}`);

    // Check if the document has a text layer
    const hasTextLayer = await checkPDFHasTextLayer(document.path);

    // Extract text from the document
    const textContent = await processDocumentText(document.path, document.id);
    console.log(
      `Text content extracted: ${textContent.isProcessed} , ${JSON.stringify(
        textContent,
      )}`,
    );
    // Process and store document vectors
    if (textContent.isProcessed && textContent.chunks.length > 0) {
      await processAndStoreDocumentVectors(document.id, textContent.chunks);
    }

    // Update document metadata
    const updatedDocument = await updateDocument(document.id, {
      isTextProcessed: textContent.isProcessed,
      textProcessingError: textContent.processingError,
      lastTextProcessed: new Date().toISOString(),
      hasTextLayer,
    });

    if (!updatedDocument) {
      throw new Error(`Failed to update document metadata for ${document.id}`);
    }

    return {
      success: true,
      document: updatedDocument,
      textContent,
    };
  } catch (error) {
    console.error('Error processing document:', error);

    // Update document metadata with error
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    const updatedDocument = await updateDocument(document.id, {
      isTextProcessed: false,
      textProcessingError: errorMessage,
      lastTextProcessed: new Date().toISOString(),
    });

    return {
      success: false,
      document: updatedDocument || document,
    };
  }
};

/**
 * Process all unprocessed documents
 */
export const processAllUnprocessedDocuments = async (
  documents: DocumentMetadata[],
): Promise<DocumentMetadata[]> => {
  const unprocessedDocuments = documents.filter(doc => !doc.isTextProcessed);

  console.log(`Found ${unprocessedDocuments.length} unprocessed documents`);

  const updatedDocuments: DocumentMetadata[] = [...documents];

  for (const document of unprocessedDocuments) {
    try {
      console.log(`Processing document: ${document.title}`);
      const result = await processDocument(document);

      // Update the document in the array
      const index = updatedDocuments.findIndex(doc => doc.id === document.id);
      if (index !== -1) {
        updatedDocuments[index] = result.document;
      }
    } catch (error) {
      console.error(`Error processing document ${document.id}:`, error);
    }
  }

  return updatedDocuments;
};

/**
 * Process a document in the background
 */
export const processDocumentInBackground = async (
  document: DocumentMetadata,
): Promise<void> => {
  try {
    // Process the document
    await processDocument(document);
  } catch (error) {
    console.error(
      `Error processing document ${document.id} in background:`,
      error,
    );
  }
};

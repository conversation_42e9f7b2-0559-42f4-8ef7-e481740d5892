import RNFS from 'react-native-fs';
import {Buffer} from 'buffer';
import {PDFDocument} from 'pdf-lib';
import MlkitOcr from 'react-native-mlkit-ocr';
import {Platform} from 'react-native';
import {convert} from 'react-native-pdf-to-image';
import {APP_DOCUMENTS_DIR} from './fileUtils';

// Use @dkdps/react-native-pdf-extractor for direct PDF text extraction

/**
 * Interface for extracted text content
 */
export interface ExtractedTextContent {
  fullText: string;
  chunks: TextChunk[];
  pageTexts: string[];
  isProcessed: boolean;
  processingError?: string;
  lastUpdated: string;
}

/**
 * Interface for text chunks
 */
export interface TextChunk {
  id: string;
  text: string;
  pageNumber: number;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  metadata?: {
    [key: string]: any;
  };
}

/**
 * Check if a PDF has a text layer using basic PDF loading
 * For more accurate detection, use the WebView parser component
 */
const checkTextLayerBasic = async (filePath: string): Promise<boolean> => {
  try {
    console.log(`[DEBUG] Starting basic text layer check for: ${filePath}`);

    // Read the file as base64
    const fileData = await RNFS.readFile(filePath, 'base64');
    console.log(
      `[DEBUG] File read successfully, size: ${fileData.length} bytes`,
    );

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(Buffer.from(fileData, 'base64'));
    console.log(`[DEBUG] PDF document loaded successfully`);

    // Get all pages
    const pages = pdfDoc.getPages();
    console.log(`[DEBUG] Found ${pages.length} pages in PDF`);

    // If we can load the PDF and it has pages, assume it might have text
    // This is a basic check - WebView parser would provide more accurate results
    const hasTextLayer = pages.length > 0;
    console.log(`[DEBUG] Basic text layer check result: ${hasTextLayer}`);

    return hasTextLayer;
  } catch (error) {
    console.error('[ERROR] Error checking PDF with basic method:', error);
    return false;
  }
};

/**
 * Check if a PDF has a text layer
 * For more accurate detection, use the PDFWebViewParser component
 */
export const checkPDFHasTextLayer = async (
  filePath: string,
): Promise<boolean> => {
  try {
    console.log(`[DEBUG] Starting PDF text layer check for: ${filePath}`);

    // Use basic PDF loading check
    const result = await checkTextLayerBasic(filePath);
    console.log(`[DEBUG] PDF text layer check completed: ${result}`);

    return result;
  } catch (error) {
    console.error('[ERROR] Error checking PDF text layer:', error);
    return false;
  }
};

/**
 * Parse PDF using WebView component (for use in React components)
 * This function returns a promise that resolves when WebView parsing is complete
 *
 * Usage:
 * 1. Import and render PDFWebViewParser component in your React component
 * 2. Use the component's onParseComplete callback to get results
 * 3. This function is provided for API compatibility but requires component integration
 */
export const parseWithWebView = (
  _filePath: string,
): Promise<ExtractedTextContent> => {
  return new Promise((resolve, _reject) => {
    // This function requires the PDFWebViewParser component to be rendered
    // and integrated with your React component lifecycle

    console.warn(
      'parseWithWebView requires PDFWebViewParser component integration',
    );

    resolve({
      fullText: '',
      chunks: [],
      pageTexts: [],
      isProcessed: false,
      processingError:
        'Use PDFWebViewParser component directly for WebView-based PDF parsing',
      lastUpdated: new Date().toISOString(),
    });
  });
};

/**
 * Create a WebView-based PDF parser instance
 * This is a helper function to guide developers to the correct approach
 */
export const createWebViewParser = () => {
  console.info(`
    To use WebView-based PDF parsing:

    1. Import the component:
       import { PDFWebViewParser } from '../components/PDFWebViewParser';

    2. Use in your React component:
       <PDFWebViewParser
         filePath="/path/to/document.pdf"
         onParseComplete={(result) => console.log(result)}
         onError={(error) => console.error(error)}
         visible={false} // Set to true for debugging
       />

    3. The component will automatically parse the PDF and call your callbacks
  `);

  return {
    info: 'Use PDFWebViewParser component for WebView-based PDF parsing',
    component: 'PDFWebViewParser',
  };
};

/**
 * Convert file path to proper URI format for Android
 */
const getFileUri = (filePath: string): string => {
  if (Platform.OS === 'android') {
    // For Android, we need to use file:// URI format
    return `file://${filePath}`;
  }
  return filePath;
};

/**
 * Extract text from a PDF using PDF-to-image conversion and MLKit OCR
 * This approach converts PDF pages to images and then extracts text using OCR
 */
export const extractTextFromPDF = async (
  filePath: string,
  documentId?: string,
): Promise<ExtractedTextContent> => {
  try {
    console.log(`[DEBUG] Starting text extraction from PDF: ${filePath}`);

    // Convert file path to proper URI format for Android
    const fileUri = getFileUri(filePath);
    console.log(`[DEBUG] Using file URI: ${fileUri}`);

    // Get PDF page count
    const pageCount = await getPDFPageCount(filePath);
    console.log(`[DEBUG] PDF has ${pageCount} pages`);

    // Convert PDF pages to images
    console.log('[DEBUG] Converting PDF pages to images...');
    const conversionResult = await convert(fileUri);
    const imagePaths = conversionResult.outputFiles || [];
    console.log(`[DEBUG] Converted ${imagePaths.length} pages to images`);

    // Extract text from each image using MLKit OCR
    const pageTexts: string[] = [];
    const allTextBlocks: any[] = [];

    for (let i = 0; i < imagePaths.length; i++) {
      try {
        console.log(`[DEBUG] Processing page ${i + 1}/${imagePaths.length}`);
        const imagePath = imagePaths[i];
        const imageUri = getFileUri(imagePath);

        // Use MLKit OCR to extract text from the image
        const ocrResult = await MlkitOcr.detectFromFile(imageUri);

        if (ocrResult && ocrResult.length > 0) {
          const pageText = ocrResult.map(block => block.text).join(' ');
          pageTexts.push(pageText);
          allTextBlocks.push(...ocrResult);

          console.log(
            `[DEBUG] Page ${i + 1}: Extracted ${pageText.length} characters`,
          );
        } else {
          pageTexts.push('');
          console.log(`[DEBUG] Page ${i + 1}: No text detected`);
        }

        // Clean up the temporary image file
        await RNFS.unlink(imagePath);
      } catch (pageError) {
        console.error(`[ERROR] Error processing page ${i + 1}:`, pageError);
        pageTexts.push('');
      }
    }

    // Combine all extracted text
    const fullText = pageTexts.join('\n\n').trim();

    if (fullText.length === 0) {
      console.log(
        '[DEBUG] No text extracted from PDF, trying alternative methods',
      );
      return await extractTextFromPDFUsingOCR(filePath);
    }

    // Create chunks from the extracted text
    const chunks = createTextChunks(fullText, pageTexts, pageCount, documentId);

    const successResult = {
      fullText,
      chunks,
      pageTexts,
      isProcessed: true,
      lastUpdated: new Date().toISOString(),
    };

    console.log(
      `[DEBUG] Text extraction completed: SUCCESS (${fullText.length} characters)`,
    );
    console.log(
      `[DEBUG] Extracted ${pageCount} pages, ${chunks.length} chunks`,
    );

    return successResult;
  } catch (error) {
    console.error(
      '[ERROR] Error extracting text from PDF using PDF-to-image OCR:',
      error,
    );

    // Fallback to alternative OCR method if PDF extraction fails
    console.log(
      '[DEBUG] PDF-to-image extraction failed, falling back to alternative OCR',
    );
    return await extractTextFromPDFUsingOCR(filePath);
  }
};

/**
 * Get the number of pages in a PDF document
 */
const getPDFPageCount = async (filePath: string): Promise<number> => {
  try {
    const fileData = await RNFS.readFile(filePath, 'base64');
    const pdfDoc = await PDFDocument.load(Buffer.from(fileData, 'base64'));
    return pdfDoc.getPageCount();
  } catch (error) {
    console.error('Error getting PDF page count:', error);
    return 1; // Default to 1 page if we can't determine
  }
};

/**
 * Create text chunks from extracted text
 */
const createTextChunks = (
  fullText: string,
  pageTexts: string[],
  pageCount: number,
  uniquePrefix?: string,
): TextChunk[] => {
  const chunks: TextChunk[] = [];

  // Generate a unique prefix if not provided
  const prefix = uniquePrefix || `doc_${Date.now()}`;

  // Create chunks for each page
  for (let pageIndex = 0; pageIndex < pageCount; pageIndex++) {
    const pageNumber = pageIndex + 1;
    const pageText = pageTexts[pageIndex] || '';

    // Split page text into chunks
    const pageChunks = chunkTextContent(pageText, 500);

    pageChunks.forEach((chunkText, chunkIndex) => {
      chunks.push({
        id: `${prefix}_page_${pageNumber}_chunk_${chunkIndex + 1}`,
        text: chunkText,
        pageNumber,
        position: {
          x: 0,
          y: chunkIndex * 100,
          width: 600,
          height: 100,
        },
        metadata: {
          chunkIndex: chunkIndex + 1,
          totalChunks: pageChunks.length,
          extractionMethod: 'pdf-extractor',
        },
      });
    });
  }

  return chunks;
};

/**
 * Extract text from a PDF using OCR
 */
export const extractTextFromPDFUsingOCR = async (
  _filePath: string,
  documentId?: string,
): Promise<ExtractedTextContent> => {
  try {
    console.log(`[DEBUG] Starting OCR text extraction for: ${_filePath}`);

    // Create a temporary directory for image extraction
    const tempDir = `${APP_DOCUMENTS_DIR}/temp_ocr_${Date.now()}`;
    console.log(`[DEBUG] Creating temporary directory: ${tempDir}`);
    await RNFS.mkdir(tempDir);

    // OCR implementation would require:
    // 1. Converting each PDF page to an image using a PDF-to-image library
    // 2. Running OCR on each image using react-native-mlkit-ocr
    // 3. Combining the results with proper positioning

    // For now, this is a placeholder implementation
    const mockImagePath = `${tempDir}/placeholder_page.png`;

    // Create a placeholder image file
    await RNFS.writeFile(mockImagePath, '', 'base64');
    console.log(`[DEBUG] Created placeholder image file`);

    try {
      // OCR processing would happen here
      // Example: const ocrResult = await MlkitOcr.recognize(mockImagePath);

      // For now, return a placeholder result indicating OCR capability
      const pageTexts = [
        '[OCR processing requires PDF-to-image conversion and MLKit OCR integration]',
      ];

      // Create placeholder chunks indicating OCR capability
      const uniquePrefix = documentId || `ocr_${Date.now()}`;
      const chunks: TextChunk[] = [
        {
          id: `${uniquePrefix}_ocr_placeholder_0`,
          text: '[OCR processing requires PDF-to-image conversion and MLKit OCR integration]',
          pageNumber: 1,
          position: {
            x: 0,
            y: 0,
            width: 100,
            height: 20,
          },
          metadata: {
            extractionMethod: 'ocr_placeholder',
            note: 'OCR requires additional PDF-to-image conversion libraries',
          },
        },
      ];

      // Clean up temporary files
      await RNFS.unlink(tempDir);
      console.log(`[DEBUG] Cleaned up temporary files`);

      const result = {
        fullText: pageTexts.join('\n\n'),
        chunks,
        pageTexts,
        isProcessed: true,
        lastUpdated: new Date().toISOString(),
      };

      console.log(`[DEBUG] OCR text extraction completed successfully`);
      console.log(`[DEBUG] Created ${chunks.length} placeholder chunks`);

      return result;
    } catch (ocrError) {
      console.error('[ERROR] OCR processing error:', ocrError);

      // Clean up temporary files
      await RNFS.unlink(tempDir);
      console.log(`[DEBUG] Cleaned up temporary files after OCR error`);

      throw ocrError;
    }
  } catch (error) {
    console.error('[ERROR] Error extracting text using OCR:', error);
    const errorResult = {
      fullText: '',
      chunks: [],
      pageTexts: [],
      isProcessed: false,
      processingError: error instanceof Error ? error.message : 'Unknown error',
      lastUpdated: new Date().toISOString(),
    };
    console.log(`[DEBUG] OCR text extraction failed, returning error result`);
    return errorResult;
  }
};

/**
 * Process text content into chunks for better handling
 */
export const chunkTextContent = (
  text: string,
  maxChunkSize: number = 1000,
): string[] => {
  if (!text) {
    return [];
  }

  // Split by paragraphs first
  const paragraphs = text.split(/\n\s*\n/);

  const chunks: string[] = [];
  let currentChunk = '';

  for (const paragraph of paragraphs) {
    // If adding this paragraph would exceed the max chunk size,
    // save the current chunk and start a new one
    if (
      currentChunk.length + paragraph.length > maxChunkSize &&
      currentChunk.length > 0
    ) {
      chunks.push(currentChunk);
      currentChunk = '';
    }

    // If the paragraph itself is longer than the max chunk size,
    // split it into sentences
    if (paragraph.length > maxChunkSize) {
      const sentences = paragraph.split(/(?<=[.!?])\s+/);

      for (const sentence of sentences) {
        if (
          currentChunk.length + sentence.length > maxChunkSize &&
          currentChunk.length > 0
        ) {
          chunks.push(currentChunk);
          currentChunk = '';
        }

        // If the sentence itself is too long, split it arbitrarily
        if (sentence.length > maxChunkSize) {
          let remainingSentence = sentence;
          while (remainingSentence.length > 0) {
            const chunkText = remainingSentence.slice(0, maxChunkSize);
            chunks.push(chunkText);
            remainingSentence = remainingSentence.slice(maxChunkSize);
          }
        } else {
          currentChunk += (currentChunk ? ' ' : '') + sentence;
        }
      }
    } else {
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
    }
  }

  // Add the last chunk if it's not empty
  if (currentChunk) {
    chunks.push(currentChunk);
  }

  return chunks;
};

/**
 * Extract text from a PDF using the appropriate method
 */
export const processDocumentText = async (
  filePath: string,
  documentId?: string,
): Promise<ExtractedTextContent> => {
  try {
    // Check if the PDF has a text layer
    const hasTextLayer = await checkPDFHasTextLayer(filePath);

    // Extract text using the appropriate method
    let extractedContent: ExtractedTextContent;

    if (hasTextLayer) {
      // Extract text from the PDF's text layer
      extractedContent = await extractTextFromPDF(filePath, documentId);
    } else {
      // Use OCR to extract text from the PDF
      extractedContent = await extractTextFromPDFUsingOCR(filePath, documentId);
    }

    return extractedContent;
  } catch (error) {
    console.error('Error processing document text:', error);
    return {
      fullText: '',
      chunks: [],
      pageTexts: [],
      isProcessed: false,
      processingError: error instanceof Error ? error.message : 'Unknown error',
      lastUpdated: new Date().toISOString(),
    };
  }
};

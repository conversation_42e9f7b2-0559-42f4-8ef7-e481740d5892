import React from 'react';
import {View, StyleSheet, ActivityIndicator} from 'react-native';
import {colors, spacing} from '../theme/theme';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import {useChatContext} from '../context/ChatContext';

interface ModelSetupProps {
  onSetupComplete?: () => void;
}

const ModelSetup: React.FC<ModelSetupProps> = ({onSetupComplete}) => {
  const {isModelReady, modelProgress, prepareModels} = useChatContext();

  const handleSetupModels = async () => {
    try {
      await prepareModels(false); // Use default model
      if (onSetupComplete) {
        onSetupComplete();
      }
    } catch (error) {
      console.error('Error setting up models:', error);
    }
  };

  const progressPercentage = Math.round(modelProgress * 100);

  return (
    <View style={styles.container}>
      <Text variant="h2" style={styles.title}>
        RAG Backend Setup Required
      </Text>

      <Text style={styles.description}>
        To use the chat feature, you need to initialize the RAG
        (Retrieval-Augmented Generation) backend. This will download the Llama
        3.2 1B model and text embeddings model for on-device AI processing. This
        is a one-time setup that requires approximately 1.5GB of storage.
      </Text>

      {modelProgress > 0 && modelProgress < 1 ? (
        <View style={styles.progressContainer}>
          <View style={styles.progressBarContainer}>
            <View
              style={[styles.progressBar, {width: `${progressPercentage}%`}]}
            />
          </View>
          <Text style={styles.progressText}>
            Initializing RAG Backend... {progressPercentage}%
          </Text>
          <ActivityIndicator color={colors.primary} style={styles.loader} />
        </View>
      ) : (
        <Button
          title={isModelReady ? 'RAG Backend Ready' : 'Initialize RAG Backend'}
          onPress={handleSetupModels}
          disabled={isModelReady}
          style={styles.button}
        />
      )}

      {isModelReady && (
        <Text style={styles.readyText}>
          The RAG backend is ready to use. You can now start chatting with
          document-aware AI!
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: spacing.lg,
    backgroundColor: colors.background,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    margin: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  title: {
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    marginBottom: spacing.lg,
    color: colors.textSecondary,
  },
  button: {
    minWidth: 200,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginVertical: spacing.md,
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: spacing.sm,
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary,
  },
  progressText: {
    marginTop: spacing.xs,
    color: colors.textSecondary,
  },
  loader: {
    marginTop: spacing.md,
  },
  readyText: {
    marginTop: spacing.md,
    color: colors.success,
    textAlign: 'center',
  },
});

export default ModelSetup;

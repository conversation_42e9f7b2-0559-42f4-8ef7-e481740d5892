import React, {useState, useEffect} from 'react';
import {View, StyleSheet, ActivityIndicator, Alert} from 'react-native';
import {colors, spacing} from '../theme/theme';
import Text from '../atoms/Text';
import Button from '../atoms/Button';
import Card from '../molecules/Card';
import {useChatContext} from '../context/ChatContext';
import {getActiveModelInfo} from '../utils/textGeneration';

const ModelManagement: React.FC = () => {
  const [modelInfo, setModelInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const {isModelReady, prepareModels, modelProgress} = useChatContext();

  useEffect(() => {
    loadModelInfo();
  }, [isModelReady]);

  const loadModelInfo = async () => {
    try {
      setIsLoading(true);
      const info = await getActiveModelInfo();
      setModelInfo(info);
    } catch (error) {
      console.error('Error loading model info:', error);
      Alert.alert('Error', 'Failed to load RAG backend information');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInitializeRAG = async () => {
    try {
      await prepareModels(false); // Use default model for now
      await loadModelInfo();
    } catch (error) {
      console.error('Error initializing RAG:', error);
      Alert.alert('Error', 'Failed to initialize RAG backend');
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator color={colors.primary} />
        <Text style={styles.loadingText}>Loading RAG backend info...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text variant="body" style={styles.description}>
        RAG (Retrieval-Augmented Generation) backend uses ExecuTorch for
        on-device AI processing with document retrieval capabilities.
      </Text>

      {modelInfo && (
        <Card style={styles.modelCard}>
          <View style={styles.modelHeader}>
            <Text variant="h4" style={styles.modelName}>
              {modelInfo.name}
            </Text>
            <View
              style={[
                styles.statusBadge,
                isModelReady ? styles.readyBadge : styles.notReadyBadge,
              ]}>
              <Text
                variant="caption"
                style={[
                  styles.statusText,
                  isModelReady ? styles.readyText : styles.notReadyText,
                ]}>
                {isModelReady ? 'Ready' : 'Not Initialized'}
              </Text>
            </View>
          </View>

          <View style={styles.modelDetails}>
            <View style={styles.detailRow}>
              <Text variant="caption" style={styles.detailLabel}>
                Type:
              </Text>
              <Text variant="caption" style={styles.detailValue}>
                {modelInfo.type}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text variant="caption" style={styles.detailLabel}>
                Model Type:
              </Text>
              <Text variant="caption" style={styles.detailValue}>
                {modelInfo.modelType}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text variant="caption" style={styles.detailLabel}>
                Backend:
              </Text>
              <Text variant="caption" style={styles.detailValue}>
                ExecuTorch + RAG
              </Text>
            </View>
          </View>

          {!isModelReady && (
            <View style={styles.actionContainer}>
              {modelProgress > 0 && modelProgress < 1 ? (
                <View style={styles.progressContainer}>
                  <View style={styles.progressBarContainer}>
                    <View
                      style={[
                        styles.progressBar,
                        {width: `${Math.round(modelProgress * 100)}%`},
                      ]}
                    />
                  </View>
                  <Text style={styles.progressText}>
                    Initializing... {Math.round(modelProgress * 100)}%
                  </Text>
                </View>
              ) : (
                <Button
                  title="Initialize RAG Backend"
                  onPress={handleInitializeRAG}
                  style={styles.actionButton}
                />
              )}
            </View>
          )}
        </Card>
      )}

      <View style={styles.infoContainer}>
        <Text variant="caption" style={styles.infoText}>
          • Uses Llama 3.2 1B QLoRA model for text generation
        </Text>
        <Text variant="caption" style={styles.infoText}>
          • Uses ALL-MiniLM-L6-v2 for text embeddings
        </Text>
        <Text variant="caption" style={styles.infoText}>
          • Supports document retrieval and augmented generation
        </Text>
        <Text variant="caption" style={styles.infoText}>
          • Runs entirely on-device for privacy
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: spacing.md,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: spacing.lg,
  },
  loadingText: {
    marginTop: spacing.sm,
    color: colors.textSecondary,
  },
  description: {
    marginBottom: spacing.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  modelCard: {
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  modelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  modelName: {
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  readyBadge: {
    backgroundColor: colors.success + '20',
  },
  notReadyBadge: {
    backgroundColor: colors.warning + '20',
  },
  statusText: {
    fontWeight: '600',
  },
  readyText: {
    color: colors.success,
  },
  notReadyText: {
    color: colors.warning,
  },
  modelDetails: {
    marginBottom: spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },
  detailLabel: {
    color: colors.textSecondary,
    fontWeight: '500',
  },
  detailValue: {
    color: colors.text,
  },
  actionContainer: {
    marginTop: spacing.sm,
  },
  actionButton: {
    alignSelf: 'stretch',
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: spacing.sm,
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary,
  },
  progressText: {
    color: colors.textSecondary,
    fontSize: 12,
  },
  infoContainer: {
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  infoText: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
});

export default ModelManagement;

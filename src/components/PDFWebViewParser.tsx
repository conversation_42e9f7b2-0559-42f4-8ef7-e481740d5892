import React, {useRef, useState, useCallback, useEffect} from 'react';
import {View, StyleSheet} from 'react-native';
import {WebView} from 'react-native-webview';
import RNFS from 'react-native-fs';
import {ExtractedTextContent} from '../utils/textExtraction';

interface PDFWebViewParserProps {
  filePath?: string;
  onParseComplete?: (result: ExtractedTextContent) => void;
  onError?: (error: string) => void;
  onReady?: () => void;
  visible?: boolean;
}

interface ParseMessage {
  type: string;
  data: any;
}

/**
 * WebView-based PDF parser component using PDF.js
 */
export const PDFWebViewParser: React.FC<PDFWebViewParserProps> = ({
  filePath,
  onParseComplete,
  onError,
  onReady,
  visible = false,
}) => {
  const webViewRef = useRef<WebView>(null);
  const [isReady, setIsReady] = useState(false);

  const handleMessage = useCallback(
    (event: any) => {
      try {
        const message: ParseMessage = JSON.parse(event.nativeEvent.data);

        switch (message.type) {
          case 'ready':
            setIsReady(true);
            onReady?.();
            break;

          case 'parseComplete':
            if (message.data.success) {
              onParseComplete?.(message.data.result);
            } else {
              onError?.(message.data.error || 'Unknown parsing error');
            }
            break;

          default:
            console.log('Unknown WebView message type:', message.type);
        }
      } catch (error) {
        console.error('Error handling WebView message:', error);
        onError?.('Failed to parse WebView message');
      }
    },
    [onParseComplete, onError, onReady],
  );

  const parsePDF = useCallback(
    async (pdfFilePath: string) => {
      if (!isReady || !webViewRef.current) {
        onError?.('WebView parser not ready');
        return;
      }

      try {
        // Read PDF file as base64
        const pdfData = await RNFS.readFile(pdfFilePath, 'base64');

        // Send PDF data to WebView for parsing
        const message = JSON.stringify({
          type: 'parsePDF',
          data: pdfData,
        });

        webViewRef.current.postMessage(message);
      } catch (error) {
        console.error('Error reading PDF file:', error);
        onError?.(
          error instanceof Error ? error.message : 'Failed to read PDF file',
        );
      }
    },
    [isReady, onError],
  );

  // Auto-parse when filePath is provided and WebView is ready
  useEffect(() => {
    if (isReady && filePath) {
      parsePDF(filePath);
    }
  }, [isReady, filePath, parsePDF]);

  // Note: For imperative access, use the component's props directly
  // The parsePDF function is automatically called when filePath prop changes

  // Create inline HTML with PDF.js
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PDF Parser</title>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
        <style>
            body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                background-color: #f5f5f5;
            }
            .status {
                padding: 10px;
                margin: 10px 0;
                border-radius: 4px;
                background-color: #e3f2fd;
                border: 1px solid #2196f3;
            }
            .error {
                background-color: #ffebee;
                border-color: #f44336;
                color: #c62828;
            }
            .success {
                background-color: #e8f5e8;
                border-color: #4caf50;
                color: #2e7d32;
            }
        </style>
    </head>
    <body>
        <div id="status" class="status">Ready to parse PDF...</div>
        
        <script>
            // Configure PDF.js worker
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
            
            const statusDiv = document.getElementById('status');
            
            function updateStatus(message, type = 'info') {
                statusDiv.textContent = message;
                statusDiv.className = \`status \${type}\`;
            }
            
            function sendMessage(type, data) {
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({ type, data }));
                } else {
                    console.log('Message to React Native:', { type, data });
                }
            }
            
            async function parsePDF(pdfData) {
                try {
                    updateStatus('Loading PDF...', 'info');
                    
                    // Convert base64 to Uint8Array
                    const binaryString = atob(pdfData);
                    const bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }
                    
                    // Load PDF document
                    const pdf = await pdfjsLib.getDocument({ data: bytes }).promise;
                    updateStatus(\`PDF loaded. Processing \${pdf.numPages} pages...\`, 'info');
                    
                    const result = {
                        numPages: pdf.numPages,
                        pages: [],
                        fullText: '',
                        chunks: []
                    };
                    
                    // Process each page
                    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                        updateStatus(\`Processing page \${pageNum}/\${pdf.numPages}...\`, 'info');
                        
                        const page = await pdf.getPage(pageNum);
                        const textContent = await page.getTextContent();
                        const viewport = page.getViewport({ scale: 1.0 });
                        
                        let pageText = '';
                        const pageChunks = [];
                        
                        // Extract text items with positioning
                        textContent.items.forEach((item, index) => {
                            if (item.str && item.str.trim()) {
                                const text = item.str.trim();
                                pageText += text + ' ';
                                
                                // Create text chunk with positioning
                                const chunk = {
                                    id: \`webview_page_\${pageNum}_chunk_\${index}\`,
                                    text: text,
                                    pageNumber: pageNum,
                                    position: {
                                        x: item.transform[4],
                                        y: viewport.height - item.transform[5], // Flip Y coordinate
                                        width: item.width || 0,
                                        height: item.height || 0
                                    },
                                    metadata: {
                                        fontName: item.fontName || '',
                                        fontSize: item.transform[0] || 0,
                                        color: item.color || [0, 0, 0],
                                        extractionMethod: 'webview_pdfjs'
                                    }
                                };
                                
                                pageChunks.push(chunk);
                            }
                        });
                        
                        result.pages.push({
                            pageNumber: pageNum,
                            text: pageText.trim(),
                            chunks: pageChunks
                        });
                        
                        result.fullText += pageText + '\\n\\n';
                        result.chunks.push(...pageChunks);
                    }
                    
                    updateStatus(\`Successfully parsed \${pdf.numPages} pages!\`, 'success');
                    
                    // Send result back to React Native
                    sendMessage('parseComplete', {
                        success: true,
                        result: {
                            fullText: result.fullText.trim(),
                            chunks: result.chunks,
                            pageTexts: result.pages.map(p => p.text),
                            isProcessed: true,
                            lastUpdated: new Date().toISOString()
                        }
                    });
                    
                } catch (error) {
                    console.error('PDF parsing error:', error);
                    updateStatus(\`Error: \${error.message}\`, 'error');
                    
                    sendMessage('parseComplete', {
                        success: false,
                        error: error.message,
                        result: {
                            fullText: '',
                            chunks: [],
                            pageTexts: [],
                            isProcessed: false,
                            processingError: error.message,
                            lastUpdated: new Date().toISOString()
                        }
                    });
                }
            }
            
            // Listen for messages from React Native
            window.addEventListener('message', function(event) {
                try {
                    const message = JSON.parse(event.data);
                    
                    if (message.type === 'parsePDF' && message.data) {
                        parsePDF(message.data);
                    }
                } catch (error) {
                    console.error('Message parsing error:', error);
                    updateStatus(\`Message error: \${error.message}\`, 'error');
                }
            });
            
            // For React Native WebView
            document.addEventListener('message', function(event) {
                try {
                    const message = JSON.parse(event.data);
                    
                    if (message.type === 'parsePDF' && message.data) {
                        parsePDF(message.data);
                    }
                } catch (error) {
                    console.error('Document message parsing error:', error);
                    updateStatus(\`Document message error: \${error.message}\`, 'error');
                }
            });
            
            // Signal that the parser is ready
            setTimeout(() => {
                sendMessage('ready', { message: 'PDF parser is ready' });
            }, 1000);
        </script>
    </body>
    </html>
  `;

  return (
    <View style={[styles.container, visible && styles.visible]}>
      <WebView
        ref={webViewRef}
        source={{html: htmlContent}}
        onMessage={handleMessage}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        mixedContentMode="compatibility"
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        style={styles.webView}
        onError={syntheticEvent => {
          const {nativeEvent} = syntheticEvent;
          console.error('WebView error:', nativeEvent);
          onError?.('WebView failed to load');
        }}
        onHttpError={syntheticEvent => {
          const {nativeEvent} = syntheticEvent;
          console.error('WebView HTTP error:', nativeEvent);
          onError?.('WebView HTTP error');
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 1,
    height: 1,
    opacity: 0,
    position: 'absolute',
    top: -1000,
    left: -1000,
  },
  visible: {
    width: '100%',
    height: 300,
    opacity: 1,
    position: 'relative',
    top: 0,
    left: 0,
  },
  webView: {
    flex: 1,
  },
});

export default PDFWebViewParser;

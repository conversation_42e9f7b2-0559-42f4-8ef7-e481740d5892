import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Alert} from 'react-native';
import {usePDFContext} from './PDFContext';
import {useSettings} from './SettingsContext';
import {
  createContextWindow,
  ContextWindow,
  addMessageToContext,
  GenerationParams,
  DEFAULT_GENERATION_PARAMS,
  initTextGeneration,
  generateText,
  addDocumentForRetrieval,
} from '../utils/textGeneration';
import {generateRAGAnswer, Citation} from '../utils/ragUtils';
import {
  getDocumentTextChunks,
  debugDocumentChunks,
} from '../utils/vectorDatabase';

// Storage key for conversations
const CONVERSATIONS_STORAGE_KEY = 'pdf_viewer_conversations';

// Message interface
export interface ChatMessage {
  id: string;
  text: string;
  createdAt: Date;
  user: {
    _id: string | number;
    name: string;
  };
  citations?: Citation[];
}

// Conversation interface
export interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  documentIds: string[];
  createdAt: Date;
  updatedAt: Date;
  context: ContextWindow;
}

// Chat context type
interface ChatContextType {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  isLoading: boolean;
  isGenerating: boolean;
  isModelReady: boolean;
  modelProgress: number;
  createNewConversation: (
    title?: string,
    documentIds?: string[],
  ) => Promise<Conversation>;
  setCurrentConversation: (conversationId: string) => void;
  sendMessage: (text: string) => Promise<void>;
  clearConversation: (conversationId: string) => Promise<void>;
  deleteConversation: (conversationId: string) => Promise<void>;
  updateConversationTitle: (
    conversationId: string,
    title: string,
  ) => Promise<void>;
  prepareModels: (useHighQuality: boolean) => Promise<void>;
}

// Default system prompt
const DEFAULT_SYSTEM_PROMPT = `You are a helpful AI assistant that helps users understand their PDF documents.
You can answer questions about the content of the documents, summarize sections, and provide insights.
Always be clear, concise, and helpful. If you don't know the answer, say so.
When citing information, use citation markers like [1], [2], etc. to indicate which document or section you're referencing.`;

// Create chat context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Chat provider props
interface ChatProviderProps {
  children: ReactNode;
}

// Use chat context hook
export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};

// Chat provider component
export const ChatProvider: React.FC<ChatProviderProps> = ({children}) => {
  const {documents} = usePDFContext();
  const {settings} = useSettings();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversationState] =
    useState<Conversation | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [isModelReady, setIsModelReady] = useState<boolean>(false);
  const [modelProgress, setModelProgress] = useState<number>(0);

  // Initialize chat context
  useEffect(() => {
    loadConversations();
    checkModels();
  }, []);

  // Effect to prepare models when settings change
  useEffect(() => {
    if (!isModelReady && settings) {
      prepareModels(settings.ai.useHighQualityModel);
    }
  }, [settings, isModelReady]);

  // Load conversations from storage
  const loadConversations = async () => {
    try {
      setIsLoading(true);
      const data = await AsyncStorage.getItem(CONVERSATIONS_STORAGE_KEY);

      if (data) {
        const loadedConversations: Conversation[] = JSON.parse(data);

        // Convert date strings to Date objects
        const conversationsWithDates = loadedConversations.map(conv => ({
          ...conv,
          createdAt: new Date(conv.createdAt),
          updatedAt: new Date(conv.updatedAt),
          messages: conv.messages.map(msg => ({
            ...msg,
            createdAt: new Date(msg.createdAt),
          })),
        }));

        setConversations(conversationsWithDates);

        // Set current conversation to the most recent one if it exists
        if (conversationsWithDates.length > 0) {
          const mostRecent = conversationsWithDates.sort(
            (a, b) => b.updatedAt.getTime() - a.updatedAt.getTime(),
          )[0];

          setCurrentConversationState(mostRecent);
        }
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
      Alert.alert('Error', 'Failed to load conversations');
    } finally {
      setIsLoading(false);
    }
  };

  // Save conversations to storage
  const saveConversations = async (conversationsToSave: Conversation[]) => {
    try {
      await AsyncStorage.setItem(
        CONVERSATIONS_STORAGE_KEY,
        JSON.stringify(conversationsToSave),
      );
    } catch (error) {
      console.error('Error saving conversations:', error);
      Alert.alert('Error', 'Failed to save conversations');
    }
  };

  // Check if RAG backend is ready
  const checkModels = async () => {
    try {
      // For RAG backend, we'll check if it's initialized
      // This is a simple check - the actual initialization happens in prepareModels
      setIsModelReady(false); // Will be set to true after successful initialization
    } catch (error) {
      console.error('Error checking RAG backend:', error);
      setIsModelReady(false);
    }
  };

  // Initialize RAG backend
  const prepareModels = async (useHighQuality: boolean) => {
    try {
      setModelProgress(0);
      console.log('Initializing RAG backend...');

      // Initialize the RAG backend with progress callback
      await initTextGeneration(progress => {
        setModelProgress(progress);
      });

      setIsModelReady(true);
      console.log('RAG backend initialized successfully');
    } catch (error) {
      console.error('Error initializing RAG backend:', error);
      Alert.alert('Error', 'Failed to initialize RAG backend');
      setIsModelReady(false);
    }
  };

  // Create a new conversation
  const createNewConversation = async (
    title?: string,
    documentIds?: string[],
  ): Promise<Conversation> => {
    try {
      // Create context window and add system message
      let context = createContextWindow();
      context = addMessageToContext(context, 'system', DEFAULT_SYSTEM_PROMPT);

      // Create new conversation
      const newConversation: Conversation = {
        id: `conv_${Date.now()}`,
        title: title || 'New Conversation',
        messages: [],
        documentIds: documentIds || [],
        createdAt: new Date(),
        updatedAt: new Date(),
        context,
      };

      // Update state
      setConversations(prevConversations => {
        const updatedConversations = [...prevConversations, newConversation];
        saveConversations(updatedConversations);
        return updatedConversations;
      });

      // Set as current conversation
      setCurrentConversationState(newConversation);

      return newConversation;
    } catch (error) {
      console.error('Error creating new conversation:', error);
      throw error;
    }
  };

  // Set current conversation
  const setCurrentConversation = (conversationId: string) => {
    const conversation = conversations.find(conv => conv.id === conversationId);

    if (conversation) {
      setCurrentConversationState(conversation);
    }
  };

  // Send a message
  const sendMessage = async (text: string) => {
    try {
      // Prepare models if not ready
      if (!isModelReady && settings) {
        await prepareModels(settings.ai.useHighQualityModel);
      }

      if (!currentConversation) {
        // Create a new conversation if none exists
        const newConversation = await createNewConversation();
        await sendMessageToConversation(newConversation, text);
      } else {
        await sendMessageToConversation(currentConversation, text);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    }
  };

  // Send a message to a specific conversation
  const sendMessageToConversation = async (
    conversation: Conversation,
    text: string,
  ) => {
    try {
      setIsGenerating(true);

      // Create user message
      const userMessage: ChatMessage = {
        id: `msg_${Date.now()}`,
        text,
        createdAt: new Date(),
        user: {
          _id: 'user',
          name: 'User',
        },
      };

      // Add user message to conversation
      const updatedMessages = [...conversation.messages, userMessage];

      // Update conversation in state
      const updatedConversation: Conversation = {
        ...conversation,
        messages: updatedMessages,
        updatedAt: new Date(),
      };

      // Update context window
      let updatedContext = addMessageToContext(
        conversation.context,
        'user',
        text,
      );

      // Update state with user message
      setConversations(prevConversations => {
        const updatedConversations = prevConversations.map(conv =>
          conv.id === conversation.id ? updatedConversation : conv,
        );
        saveConversations(updatedConversations);
        return updatedConversations;
      });

      setCurrentConversationState(updatedConversation);

      // Generate response
      let responseText = '';
      const generationParams: GenerationParams = {
        ...DEFAULT_GENERATION_PARAMS,
        maxTokens: 512,
      };

      // Add documents to RAG for retrieval if available
      const relevantDocIds =
        conversation.documentIds.length > 0
          ? conversation.documentIds
          : documents.map(doc => doc.id);

      const relevantDocs = documents.filter(doc =>
        relevantDocIds.includes(doc.id),
      );

      // Debug: Show all document chunks in database
      await debugDocumentChunks();

      // Add documents to RAG backend for retrieval
      for (const doc of relevantDocs) {
        try {
          console.log(
            `[DEBUG] Looking for chunks for document ID: ${doc.id}, title: ${doc.title}`,
          );

          // Get actual text chunks from the database
          const textChunks = await getDocumentTextChunks(doc.id);
          console.log(
            `[DEBUG] Found ${textChunks.length} chunks for document ${doc.id}`,
          );

          if (textChunks.length > 0) {
            // Combine chunks into meaningful content for RAG
            const documentContent = `Document: ${doc.title}\nAuthor: ${
              doc.author
            }\nPages: ${doc.pageCount}\n\nContent:\n${textChunks
              .map(chunk => chunk.text)
              .join('\n\n')}`;

            await addDocumentForRetrieval(documentContent, {
              documentId: doc.id,
              title: doc.title,
              type: 'pdf',
              chunkCount: textChunks.length,
            });

            console.log(
              `[SUCCESS] Added document ${doc.title} to RAG with ${textChunks.length} chunks`,
            );
          } else {
            console.warn(
              `[WARNING] No text chunks found for document ${doc.title} (ID: ${doc.id}), using metadata only`,
            );
            // Fallback to metadata if no chunks are available
            const documentContent = `Document: ${doc.title}\nAuthor: ${doc.author}\nPages: ${doc.pageCount}`;
            await addDocumentForRetrieval(documentContent, {
              documentId: doc.id,
              title: doc.title,
              type: 'pdf',
            });
          }
        } catch (error) {
          console.warn('Failed to add document to RAG:', error);
        }
      }

      // Generate response using RAG backend
      let streamingMessageId = `msg_${Date.now() + 1}`;
      let lastUpdateTime = 0;
      const UPDATE_THROTTLE_MS = 50; // Throttle updates to every 50ms for smoother streaming

      const response = await generateText(text, generationParams, token => {
        responseText += token;
        console.log(
          '[DEBUG] Received token, current response length:',
          responseText.length,
        );

        // Throttle updates to prevent excessive re-renders
        const now = Date.now();
        if (now - lastUpdateTime < UPDATE_THROTTLE_MS) {
          console.log('[DEBUG] Throttling update, skipping UI refresh');
          return;
        }
        lastUpdateTime = now;
        console.log('[DEBUG] Updating UI with streaming text');

        // Create temporary assistant message for streaming effect
        const tempAssistantMessage: ChatMessage = {
          id: streamingMessageId,
          text: responseText,
          createdAt: new Date(),
          user: {
            _id: 'assistant',
            name: 'Assistant',
          },
        };

        // Update conversation in state with temporary message
        const tempUpdatedConversation: Conversation = {
          ...updatedConversation,
          messages: [...updatedMessages, tempAssistantMessage],
        };

        setCurrentConversationState(tempUpdatedConversation);
      });

      // Create final assistant message (reuse the streaming message ID to avoid duplication)
      const assistantMessage: ChatMessage = {
        id: streamingMessageId,
        text: response,
        createdAt: new Date(),
        user: {
          _id: 'assistant',
          name: 'Assistant',
        },
      };

      // Update context window with assistant response
      updatedContext = addMessageToContext(
        updatedContext,
        'assistant',
        response,
      );

      // Update conversation with final message and context
      const finalUpdatedConversation: Conversation = {
        ...updatedConversation,
        messages: [...updatedMessages, assistantMessage],
        context: updatedContext,
        updatedAt: new Date(),
      };

      // Update state
      setConversations(prevConversations => {
        const updatedConversations = prevConversations.map(conv =>
          conv.id === conversation.id ? finalUpdatedConversation : conv,
        );
        saveConversations(updatedConversations);
        return updatedConversations;
      });

      setCurrentConversationState(finalUpdatedConversation);
    } catch (error) {
      console.error('Error sending message to conversation:', error);
      Alert.alert('Error', 'Failed to generate response');
    } finally {
      setIsGenerating(false);
    }
  };

  // Clear a conversation
  const clearConversation = async (conversationId: string) => {
    try {
      // Find conversation
      const conversation = conversations.find(
        conv => conv.id === conversationId,
      );

      if (!conversation) {
        return;
      }

      // Create new context window and add system message
      let newContext = createContextWindow();
      newContext = addMessageToContext(
        newContext,
        'system',
        DEFAULT_SYSTEM_PROMPT,
      );

      // Update conversation
      const updatedConversation: Conversation = {
        ...conversation,
        messages: [],
        context: newContext,
        updatedAt: new Date(),
      };

      // Update state
      setConversations(prevConversations => {
        const updatedConversations = prevConversations.map(conv =>
          conv.id === conversationId ? updatedConversation : conv,
        );
        saveConversations(updatedConversations);
        return updatedConversations;
      });

      // Update current conversation if needed
      if (currentConversation?.id === conversationId) {
        setCurrentConversationState(updatedConversation);
      }
    } catch (error) {
      console.error('Error clearing conversation:', error);
      Alert.alert('Error', 'Failed to clear conversation');
    }
  };

  // Delete a conversation
  const deleteConversation = async (conversationId: string) => {
    try {
      // Update state
      setConversations(prevConversations => {
        const updatedConversations = prevConversations.filter(
          conv => conv.id !== conversationId,
        );
        saveConversations(updatedConversations);

        // If we deleted the current conversation, set a new one
        if (currentConversation?.id === conversationId) {
          if (updatedConversations.length > 0) {
            setCurrentConversationState(updatedConversations[0]);
          } else {
            setCurrentConversationState(null);
          }
        }

        return updatedConversations;
      });
    } catch (error) {
      console.error('Error deleting conversation:', error);
      Alert.alert('Error', 'Failed to delete conversation');
    }
  };

  // Update conversation title
  const updateConversationTitle = async (
    conversationId: string,
    title: string,
  ) => {
    try {
      // Update state
      setConversations(prevConversations => {
        const updatedConversations = prevConversations.map(conv =>
          conv.id === conversationId
            ? {...conv, title, updatedAt: new Date()}
            : conv,
        );
        saveConversations(updatedConversations);

        // Update current conversation if needed
        if (currentConversation?.id === conversationId) {
          setCurrentConversationState({
            ...currentConversation,
            title,
            updatedAt: new Date(),
          });
        }

        return updatedConversations;
      });
    } catch (error) {
      console.error('Error updating conversation title:', error);
      Alert.alert('Error', 'Failed to update conversation title');
    }
  };

  // Context value
  const value: ChatContextType = {
    conversations,
    currentConversation,
    isLoading,
    isGenerating,
    isModelReady,
    modelProgress,
    createNewConversation,
    setCurrentConversation,
    sendMessage,
    clearConversation,
    deleteConversation,
    updateConversationTitle,
    prepareModels,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

export default ChatContext;

pluginManagement {
  includeBuild("../node_modules/@react-native/gradle-plugin")
  def expoPluginsPath = new File(
    providers.exec {
      workingDir(rootDir)
      commandLine("node", "--print", "require.resolve('expo-modules-autolinking/package.json', { paths: [require.resolve('expo/package.json')] })")
    }.standardOutput.asText.get().trim(),
    "../android/expo-gradle-plugin"
  ).absolutePath
  includeBuild(expoPluginsPath)
}
plugins {
  id("com.facebook.react.settings")
  id("expo-autolinking-settings")
}
extensions.configure(com.facebook.react.ReactSettingsExtension){ ex ->
  ex.autolinkLibrariesFromCommand(expoAutolinking.rnConfigCommand)
}
rootProject.name = 'PDFViewer'
include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')
include ':react-native-vector-icons'
project(':react-native-vector-icons').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-vector-icons/android')
expoAutolinking.useExpoModules()
// Removed problematic lines that are not available in current Expo version
